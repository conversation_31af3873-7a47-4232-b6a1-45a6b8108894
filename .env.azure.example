# Azure Blob Storage Logging Configuration Example
# Copy this file to .env and configure your Azure Storage details to enable remote logging

# =============================================================================
# AZURE BLOB STORAGE LOGGING CONFIGURATION
# =============================================================================

# Option 1: Use Azure Storage Connection String (recommended)
# Get this from Azure Portal > Storage Account > Access Keys > Connection String
AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=https;AccountName=yourstorageaccount;AccountKey=your-storage-key;EndpointSuffix=core.windows.net"

# Option 2: Use Storage Account Name and Key separately
# AZURE_STORAGE_ACCOUNT_NAME="yourstorageaccount"
# AZURE_STORAGE_ACCOUNT_KEY="your-storage-key"

# Container name for storing log files (will be created if it doesn't exist)
AZURE_LOG_CONTAINER_NAME="autolodge-logs"

# Prefix for log blob names (e.g., "score" creates files like "score_2025-06-06_10-30-00.log")
AZURE_LOG_BLOB_PREFIX="score"

# =============================================================================
# USAGE INSTRUCTIONS
# =============================================================================

# 1. Create an Azure Storage Account:
#    - Go to Azure Portal
#    - Create a new Storage Account or use an existing one
#    - Note the account name and access key

# 2. Configure environment variables:
#    - Copy this file to .env
#    - Set either AZURE_STORAGE_CONNECTION_STRING or both AZURE_STORAGE_ACCOUNT_NAME and AZURE_STORAGE_ACCOUNT_KEY
#    - Optionally customize AZURE_LOG_CONTAINER_NAME and AZURE_LOG_BLOB_PREFIX

# 3. Run your application:
#    - The logging system will automatically detect Azure configuration
#    - Logs will be uploaded to Azure Blob Storage in addition to console output
#    - If Azure is not configured, it will fall back to local file logging

# =============================================================================
# FEATURES
# =============================================================================

# ✅ Dual output: Console (unchanged) + Azure Blob Storage
# ✅ Automatic fallback to local files if Azure is not configured
# ✅ Timestamped log files with same naming convention
# ✅ Buffered uploads for performance (uploads every 30 seconds or 100 entries)
# ✅ Thread-safe logging with proper error handling
# ✅ Emoji conventions and formatting preserved
# ✅ Comprehensive error messages and configuration logging

# =============================================================================
# TROUBLESHOOTING
# =============================================================================

# If you see "Azure Storage connection string not configured":
# - Make sure you've set the environment variables correctly
# - Check that your .env file is in the correct location
# - Verify your Azure Storage account credentials

# If you see "Azure Blob Storage dependencies not available":
# - Make sure azure-storage-blob package is installed
# - Check that configs/autolodge.yaml includes azure-storage-blob==12.19.0

# If uploads fail:
# - Check your Azure Storage account permissions
# - Verify the connection string is correct
# - Check network connectivity to Azure
