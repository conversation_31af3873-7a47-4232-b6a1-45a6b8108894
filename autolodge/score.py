"""
Optimized ML inference service for text classification.

This module provides a high-performance text classification service that processes
treatment descriptions and predicts categories using a trained Keras model with
comprehensive preprocessing pipeline.
"""

import json
import os
import pickle
import re
from dataclasses import dataclass
from datetime import datetime
from importlib.resources import files
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import nltk
import numpy as np
import pandas as pd
import tensorflow as tf
from loguru import logger
from nltk import PorterStemmer
from nltk.corpus import stopwords
from symspellpy import SymSpell, Verbosity
from tensorflow import keras

# Configure TensorFlow to use CPU only and suppress CUDA warnings
# This fixes the "Could not load dynamic library 'libcudart.so.11.0'" error
# by forcing TensorFlow to use CPU-only execution mode
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'  # Disable GPU visibility
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # Suppress all TensorFlow messages except errors


# Additional TensorFlow configuration to ensure CPU-only execution
tf.config.set_visible_devices([], 'GPU')


# Configure Loguru logging
def setup_logging():
    """Configure Loguru logging with timestamped files and rotation."""
    # Remove default handler
    logger.remove()

    # Create logs directory if it doesn't exist
    logs_dir = Path(__file__).parent / 'logs'
    logs_dir.mkdir(exist_ok=True)

    # Generate timestamp for log filename
    timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
    log_file = logs_dir / f'score_{timestamp}.log'

    # Add console handler with colored output
    logger.add(
        sink=lambda msg: print(msg, end=''),
        format='<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>',
        level='INFO',
        colorize=True,
    )

    # Add file handler with rotation
    logger.add(
        sink=str(log_file),
        format='{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}',
        level='DEBUG',
        rotation='1 day',  # Rotate daily
        retention='30 days',  # Keep logs for 30 days
        compression='zip',  # Compress old logs
        enqueue=True,  # Thread-safe logging
        backtrace=True,  # Include traceback in error logs
        diagnose=True,  # Include variable values in traceback
    )

    # Add size-based rotation as backup
    logger.add(
        sink=str(logs_dir / f'score_size_rotated_{timestamp}.log'),
        format='{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}',
        level='DEBUG',
        rotation='10 MB',  # Rotate when file reaches 10MB
        retention=10,  # Keep 10 rotated files
        compression='zip',
        enqueue=True,
    )

    logger.info(f'Logging configured - Log file: {log_file}')
    return log_file


# Initialize logging
setup_logging()


def _get_symspell_dictionary_path() -> str:
    """
    Get the path to the SymSpell dictionary file using modern importlib.resources.

    Returns:
        str: Path to the frequency_dictionary_en_82_765.txt file

    Raises:
        FileNotFoundError: If the dictionary file cannot be found
    """
    try:
        # Use modern importlib.resources approach
        symspell_files = files('symspellpy')
        dictionary_file = symspell_files / 'frequency_dictionary_en_82_765.txt'

        # Convert to string path - this works for both Python 3.9+ and older versions
        return str(dictionary_file)
    except Exception as e:
        logger.warning(f'Failed to use importlib.resources: {e}. Falling back to pkg_resources.')

    # Fallback to pkg_resources if importlib.resources is not available
    try:
        import pkg_resources

        return pkg_resources.resource_filename('symspellpy', 'frequency_dictionary_en_82_765.txt')
    except ImportError:
        raise FileNotFoundError(
            'Cannot locate SymSpell dictionary file. Please ensure symspellpy is properly installed.'
        )


@dataclass
class Config:
    """Configuration management for the scoring service."""

    # Local paths for resources and models
    # All files are now co-located in the resources/ directory at repository root
    # Use paths relative to the repository root to work from any working directory
    UTILS_PATH: str = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'resources')
    MODEL_PATH: str = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'resources', 'autolodge_20250605.h5')

    # Model Configuration
    BATCH_SIZE: int = 8
    TOP_K_PREDICTIONS: int = 5
    API_VERSION: int = 7

    # Spell Checker Configuration
    MAX_EDIT_DISTANCE: int = 3
    MIN_WORD_LENGTH_FOR_SPELL_CHECK: int = 10

    # Required utility files
    REQUIRED_UTILS: Optional[List[str]] = None

    def __post_init__(self):
        """Initialize computed fields."""
        if self.REQUIRED_UTILS is None:
            self.REQUIRED_UTILS = ['le.pkl', 'tk.pkl', 'abbr.csv', 'corpus.pkl']


class TextPreprocessor:
    """Optimized text preprocessing pipeline for treatment descriptions."""

    def __init__(self, config: Config):
        self.config = config
        self._compiled_patterns: Dict[str, Any] = {}
        self._abbreviation_table: Optional[pd.DataFrame] = None
        self._stopwords_to_remove: set = set()
        self._initialize_patterns()
        self._initialize_stopwords()

    def _initialize_patterns(self) -> None:
        """Initialize and cache compiled regex patterns for better performance."""
        self._compiled_patterns = {
            # Customized RE rules
            'cons_pattern': re.compile(r'(?<=\b)Cons(?=[A-Z]+)'),  # ConsFS, ConsCCRpt
            'rpt_pattern': re.compile(r'(?:(?<=\b)|(?<=[A-Z]))[r,R]pt'),  # Rpt
            'ordinal_pattern': re.compile(r'1st|2nd|3rd', flags=re.I),  # 1st|2nd|3rd
            'size_pattern': re.compile(r'[x|X]+([s|S]mall|[l|L]arge)'),  # xSmall, xxLarge
            'z_pattern': re.compile(r'z+(?=[A-Z])'),  # zComfortiz
            'slash_pattern': re.compile(r'(?<=\b)([A-Z])\/([A-Z])(?=\b)'),  # I/D
            # Punctuation and tokenization patterns
            'punctuation_pattern': re.compile(r'[!"#$%&\'()*+\-,/:;<=>?@[\\\]^_`{|}~\n]+'),
            'whitespace_pattern': re.compile(r'\s+'),
            'token_pattern': re.compile(
                r'(?<=\b)[a-z]\.[a-z](?:\.(?:[a-z]|\d))*\.?(?=\s|$)|(?<=\b)[a-z]{1,2}\d{1,2}[a-z]?(?=\s|$)|[a-z]+'
            ),
        }

    def _initialize_stopwords(self) -> None:
        """Initialize comprehensive stopwords list for removal."""
        self._stopwords_to_remove = set()

        # Single letters
        self._stopwords_to_remove.update(list('abcdefghijklmnopqrstuvwxyz'))

        # Units and measurements
        self._stopwords_to_remove.update([
            'kg',
            'mg',
            'gm',
            'mgmg',
            'ml',
            'litre',
            'cm',
            'km',
            'gram',
            'meter',
            'pm',
            'am',
            'mm',
            'mcg',
            'mgc',
        ])

        # Months
        self._stopwords_to_remove.update([
            'jan',
            'feb',
            'mar',
            'apr',
            'may',
            'jun',
            'jul',
            'aug',
            'sep',
            'oct',
            'nov',
            'dec',
        ])

        # Numbers as words
        self._stopwords_to_remove.update([
            'one',
            'two',
            'three',
            'four',
            'five',
            'six',
            'seven',
            'eight',
            'nine',
            'ten',
        ])

        # Days of week
        self._stopwords_to_remove.update([
            'monday',
            'tuesday',
            'wednesday',
            'thursday',
            'friday',
            'mon',
            'tue',
            'wed',
            'thu',
            'fri',
        ])

        # Other common words
        self._stopwords_to_remove.update(['per', 'yr', 'another'])

    def _load_abbreviation_table(self, utils_path: str) -> pd.DataFrame:
        """Load and cache abbreviation table for efficient lookups."""
        if self._abbreviation_table is None:
            abbr_path = os.path.join(utils_path, 'abbr.csv')
            self._abbreviation_table = pd.read_csv(abbr_path, sep='|', header=0)
            # Filter out single capital letters
            self._abbreviation_table = self._abbreviation_table[
                ~self._abbreviation_table.Abbreviation.str.contains('^[A-Z]$')
            ]
        return self._abbreviation_table

    def _apply_regex_rules(self, text: str) -> str:
        """Apply regex transformation rules to text."""
        # Apply all regex patterns
        text = self._compiled_patterns['cons_pattern'].sub('consultation ', text)
        text = self._compiled_patterns['rpt_pattern'].sub('repeat ', text)
        text = self._compiled_patterns['ordinal_pattern'].sub('', text)
        text = self._compiled_patterns['size_pattern'].sub(r'\1', text)
        text = self._compiled_patterns['z_pattern'].sub('', text)
        text = self._compiled_patterns['slash_pattern'].sub(r'\1.\2', text)
        return text

    def _expand_abbreviations(self, text: str, utils_path: str) -> str:
        """Expand abbreviations using cached lookup table."""
        abbr_table = self._load_abbreviation_table(utils_path)

        # Case-sensitive abbreviations
        cs_abbr_list = abbr_table[['Abbreviation', 'FullText']][abbr_table.Comment == 'CS']
        for _, row in cs_abbr_list.iterrows():
            old_abbr = f' {row["Abbreviation"]} '
            new_full = f' {row["FullText"]} '
            text = text.replace(old_abbr, new_full)

        # Case-insensitive abbreviations
        lc_abbr_list = abbr_table[['Abbreviation', 'FullText']][abbr_table.Comment.isna()].copy()
        lc_abbr_list['Abbreviation'] = lc_abbr_list['Abbreviation'].str.lower()

        text_lower = text.lower()
        for _, row in lc_abbr_list.iterrows():
            old_abbr = f' {row["Abbreviation"]} '
            new_full = f' {row["FullText"]} '
            text_lower = text_lower.replace(old_abbr, new_full)

        return text_lower

    def _remove_punctuation(self, text: str) -> str:
        """Remove punctuation and normalize whitespace."""
        text = self._compiled_patterns['punctuation_pattern'].sub(' ', text)
        text = self._compiled_patterns['whitespace_pattern'].sub(' ', text)
        return text.strip()

    def _tokenize_text(self, text: str) -> List[str]:
        """Extract tokens using optimized regex pattern."""
        return self._compiled_patterns['token_pattern'].findall(text)

    def _filter_tokens(self, tokens: List[str], stopwords_set: set) -> List[str]:
        """Filter out stopwords and unwanted tokens."""
        # Remove NLTK stopwords
        tokens = [word for word in tokens if word not in stopwords_set]

        # Remove additional stopwords
        tokens = [word for word in tokens if word not in self._stopwords_to_remove]

        return tokens

    def _apply_spell_correction(self, tokens: List[str], spell_checker: SymSpell, corpus: list) -> List[str]:
        """Apply spell correction to long tokens."""
        corrected_tokens = []
        for word in tokens:
            if len(word) >= self.config.MIN_WORD_LENGTH_FOR_SPELL_CHECK and spell_checker is not None:
                suggestions = spell_checker.lookup(word, Verbosity.TOP, max_edit_distance=1, include_unknown=True)
                if suggestions and suggestions[0].distance == 1 and suggestions[0].term in corpus:
                    corrected_tokens.append(suggestions[0].term)
                else:
                    corrected_tokens.append(word)
            else:
                corrected_tokens.append(word)
        return corrected_tokens

    def _apply_stemming(self, tokens: List[str]) -> List[str]:
        """Apply Porter stemming to tokens."""
        stemmer = PorterStemmer()
        return [stemmer.stem(word) for word in tokens]

    def process_text(
        self, text: str, utils_path: str, stopwords_set: set, spell_checker: SymSpell, corpus: list
    ) -> List[str]:
        """
        Process a single text through the complete preprocessing pipeline.

        Args:
            text: Input text to process
            utils_path: Path to utility files
            stopwords_set: Set of NLTK stopwords
            spell_checker: SymSpell instance for spell correction
            corpus: Corpus for spell checking validation

        Returns:
            List of processed tokens
        """
        # 1. Apply regex rules
        text = self._apply_regex_rules(text)

        # 2. Expand abbreviations
        text = self._expand_abbreviations(text, utils_path)

        # 3. Remove punctuation
        text = self._remove_punctuation(text)

        # 4. Tokenize
        tokens = self._tokenize_text(text)

        # 5. Filter stopwords
        tokens = self._filter_tokens(tokens, stopwords_set)

        # 6. Apply spell correction
        tokens = self._apply_spell_correction(tokens, spell_checker, corpus)

        # 7. Apply stemming
        tokens = self._apply_stemming(tokens)

        return tokens


class ModelResourceManager:
    """
    Manages ML model and preprocessing resources with proper lifecycle management.

    All resources (model file and utility files) are loaded from the
    resources/ directory at repository root, providing a self-contained deployment structure.
    """

    def __init__(self, config: Config):
        self.config = config
        self._model = None
        self._tokenizer = None
        self._label_encoder = None
        self._spell_checker = None
        self._stopwords = None
        self._corpus = None
        self._initialized = False

    @property
    def model(self) -> Any:
        """Get the loaded Keras model."""
        if self._model is None:
            raise RuntimeError('Model not initialized. Call initialize() first.')
        return self._model

    @property
    def tokenizer(self) -> Any:
        """Get the loaded tokenizer."""
        if self._tokenizer is None:
            raise RuntimeError('Tokenizer not initialized. Call initialize() first.')
        return self._tokenizer

    @property
    def label_encoder(self) -> Any:
        """Get the loaded label encoder."""
        if self._label_encoder is None:
            raise RuntimeError('Label encoder not initialized. Call initialize() first.')
        return self._label_encoder

    @property
    def spell_checker(self) -> SymSpell:
        """Get the loaded spell checker."""
        if self._spell_checker is None:
            raise RuntimeError('Spell checker not initialized. Call initialize() first.')
        return self._spell_checker

    @property
    def stopwords(self) -> set:
        """Get the loaded stopwords."""
        if self._stopwords is None:
            raise RuntimeError('Stopwords not initialized. Call initialize() first.')
        return self._stopwords

    @property
    def corpus(self) -> list:
        """Get the loaded corpus."""
        if self._corpus is None:
            raise RuntimeError('Corpus not initialized. Call initialize() first.')
        return self._corpus

    @property
    def is_initialized(self) -> bool:
        """Check if all resources are initialized."""
        return self._initialized

    def _load_model_from_path(self, model_path: str) -> Any:
        """
        Load Keras model from the local file path.

        Args:
            model_path: Path to the model directory or file (relative to Python/ directory)

        Returns:
            keras.Model: Loaded Keras model

        Raises:
            Exception: If model loading fails
        """
        try:
            # Handle different model path structures
            if os.path.isdir(model_path):
                # Look for common model file names in the directory
                model_files = []
                for ext in ['.h5', '.keras', '.pb']:
                    model_files.extend([f for f in os.listdir(model_path) if f.endswith(ext)])

                if model_files:
                    model_file_path = os.path.join(model_path, model_files[0])
                else:
                    # Try loading the directory as a SavedModel
                    model_file_path = model_path
            else:
                model_file_path = model_path

            logger.info(f'Loading Keras model from: {model_file_path}')
            model = keras.models.load_model(model_file_path)
            logger.info('Keras model loaded successfully')
            return model

        except Exception as e:
            logger.error(f'Failed to load Keras model: {str(e)}')
            raise

    def _load_preprocessing_components(self) -> Tuple[Any, Any, set, list, SymSpell]:
        """
        Load all preprocessing components from local resources directory.

        Loads tokenizer, label encoder, stopwords, corpus, and spell checker
        from the resources/ directory at repository root.

        Returns:
            Tuple containing tokenizer, label encoder, stopwords, corpus, and spell checker

        Raises:
            Exception: If loading any component fails
        """
        try:
            # Load tokenizer
            logger.info('Loading tokenizer...')
            with open(os.path.join(self.config.UTILS_PATH, 'tk.pkl'), 'rb') as pkl_file:
                tokenizer = pickle.load(pkl_file)

            # Load label encoder
            logger.info('Loading label encoder...')
            with open(os.path.join(self.config.UTILS_PATH, 'le.pkl'), 'rb') as pkl_file:
                label_encoder = pickle.load(pkl_file)

            # Load stopwords
            logger.info('Loading stopwords...')
            stopwords_set = set(stopwords.words('english'))

            # Load corpus
            logger.info('Loading corpus...')
            with open(os.path.join(self.config.UTILS_PATH, 'corpus.pkl'), 'rb') as pkl_file:
                corpus_data = pickle.load(pkl_file)

            # Initialize spell checker
            logger.info('Initializing spell checker...')
            dictionary_path = _get_symspell_dictionary_path()
            spell_checker = SymSpell(max_dictionary_edit_distance=self.config.MAX_EDIT_DISTANCE)
            spell_checker.load_dictionary(dictionary_path, term_index=0, count_index=1)

            logger.info('All preprocessing components loaded successfully')
            return tokenizer, label_encoder, stopwords_set, corpus_data, spell_checker

        except Exception as e:
            logger.error(f'Failed to load preprocessing components: {str(e)}')
            raise

    def initialize(self) -> None:
        """
        Initialize the scoring service using local files from resources/ directory.

        Loads the Keras model and all preprocessing components (tokenizer, label encoder,
        corpus, and spell checker) from the self-contained resources directory.
        """
        try:
            logger.info('Starting initialization with local files...')

            # Download NLTK data
            logger.info('Downloading NLTK stopwords...')
            nltk.download('stopwords')

            # Load the Keras model from local path
            logger.info(f'Loading model from: {self.config.MODEL_PATH}')
            self._model = self._load_model_from_path(self.config.MODEL_PATH)

            # Load all preprocessing components
            self._tokenizer, self._label_encoder, self._stopwords, self._corpus, self._spell_checker = (
                self._load_preprocessing_components()
            )

            self._initialized = True
            logger.info('Initialization completed successfully with local files')

        except Exception as e:
            logger.error(f'Initialization failed: {str(e)}')
            raise

    def initialize_local(self, model_path: Optional[str] = None) -> None:
        """
        Initialize for local testing mode - load model and components from local files.

        Uses the same local resources directory structure as the main initialize() method.
        All files are loaded from resources/ directory at repository root.

        Args:
            model_path: Optional custom model path. If None, uses config.MODEL_PATH
        """
        try:
            logger.info('Running in local testing mode...')

            # Use provided model path or default from config
            if model_path is None:
                model_path = self.config.MODEL_PATH

            logger.info(f'Loading model from: {model_path}')
            print(Path(model_path).resolve())

            # Load model directly
            self._model = keras.models.load_model(model_path)

            # Load preprocessing components
            with open(os.path.join(self.config.UTILS_PATH, 'tk.pkl'), 'rb') as pkl_file:
                self._tokenizer = pickle.load(pkl_file)

            with open(os.path.join(self.config.UTILS_PATH, 'le.pkl'), 'rb') as pkl_file:
                self._label_encoder = pickle.load(pkl_file)

            self._stopwords = set(stopwords.words('english'))

            with open(os.path.join(self.config.UTILS_PATH, 'corpus.pkl'), 'rb') as pkl_file:
                self._corpus = pickle.load(pkl_file)

            dictionary_path = _get_symspell_dictionary_path()
            self._spell_checker = SymSpell(max_dictionary_edit_distance=self.config.MAX_EDIT_DISTANCE)
            self._spell_checker.load_dictionary(dictionary_path, term_index=0, count_index=1)

            self._initialized = True
            logger.info('Local initialization completed successfully')

        except Exception as e:
            logger.error(f'Local initialization failed: {str(e)}')
            raise


# Global instance for backward compatibility
_resource_manager: Optional[ModelResourceManager] = None


def _get_resource_manager() -> ModelResourceManager:
    """Get the global resource manager instance."""
    global _resource_manager
    if _resource_manager is None:
        _resource_manager = ModelResourceManager(Config())
    return _resource_manager


def init() -> None:
    """Initialize the scoring service using local files."""
    resource_manager = _get_resource_manager()
    resource_manager.initialize()


def preprocess(input_data: str) -> Tuple[np.ndarray, List[Any]]:
    """
    Preprocess input data for model inference using optimized pipeline.

    Args:
        input_data: JSON string containing input data

    Returns:
        Tuple of processed features and pair IDs

    Raises:
        Exception: If preprocessing fails
    """
    try:
        # Get resource manager and components
        resource_manager = _get_resource_manager()
        if not resource_manager.is_initialized:
            raise RuntimeError('Resource manager not initialized. Call init() first.')

        # Initialize text preprocessor
        config = Config()
        text_processor = TextPreprocessor(config)

        # Parse input data
        logger.info('Starting preprocessing pipeline...')
        data = json.loads(input_data)
        treatments = [item['T'] for item in data]
        pair_ids = [item['Pair_ID'] for item in data]

        # Join treatments with separator for batch processing
        combined_text = ' linebreak '.join(treatments)

        # Process the combined text through the optimized pipeline
        processed_tokens = text_processor.process_text(
            text=combined_text,
            utils_path=resource_manager.config.UTILS_PATH,
            stopwords_set=resource_manager.stopwords,
            spell_checker=resource_manager.spell_checker,
            corpus=resource_manager.corpus,
        )

        # Split back into individual treatment tokens
        token_groups = ' '.join(processed_tokens).split('linebreak')

        # Tokenize using the loaded tokenizer
        if resource_manager.tokenizer is not None:
            feature_matrix = resource_manager.tokenizer.texts_to_matrix(token_groups, mode='binary')
        else:
            raise RuntimeError('Tokenizer not available')

        logger.info(f'Preprocessing completed for {len(pair_ids)} items')
        return feature_matrix, pair_ids

    except Exception as e:
        logger.error(f'Preprocessing failed: {str(e)}')
        raise


def preprocess_legacy(input_data: str) -> Tuple[np.ndarray, List[Any]]:
    """
    Legacy preprocessing function for backward compatibility.

    This function maintains the original preprocessing logic for comparison
    and fallback purposes.

    Args:
        input_data: JSON string containing input data

    Returns:
        Tuple of processed features and pair IDs

    Raises:
        Exception: If preprocessing fails
    """
    try:
        # Get resource manager
        resource_manager = _get_resource_manager()
        if not resource_manager.is_initialized:
            raise RuntimeError('Resource manager not initialized. Call init() first.')

        ################################ 0. Prep ################################
        data = json.loads(input_data)
        treatments = [item['T'] for item in data]
        pairids = [item['Pair_ID'] for item in data]
        s = ' linebreak '.join(treatments)

        ################################# 1. RE #################################
        logger.debug(f'start 1: {datetime.now().strftime("%H:%M:%S")}')
        # Customized RE rules
        # ConsFS, ConsCCRpt
        p1 = re.compile(r'(?<=\b)Cons(?=[A-Z]+)')
        # Rpt
        p2 = re.compile(r'(?:(?<=\b)|(?<=[A-Z]))[r,R]pt')
        # 1st|2nd|3rd
        p3 = re.compile(r'1st|2nd|3rd', flags=re.I)
        # xSmall, xxLarge
        p4 = re.compile(r'[x|X]+([s|S]mall|[l|L]arge)')
        # zComfortiz
        p5 = re.compile(r'z+(?=[A-Z])')
        # I/D
        p6 = re.compile(r'(?<=\b)([A-Z])\/([A-Z])(?=\b)')

        # Apply RE rules prior
        s = p1.sub(repl='consultation ', string=s)
        s = p2.sub(repl='repeat ', string=s)
        s = p3.sub(repl='', string=s)
        s = p4.sub(repl=r'\1', string=s)
        s = p5.sub(repl=r'', string=s)
        s = p6.sub(repl=r'\1.\2', string=s)

        ################################# 2. Abbr #################################
        logger.debug(f'start 2: {datetime.now().strftime("%H:%M:%S")}')

        abbr_table = pd.read_csv(os.path.join(resource_manager.config.UTILS_PATH, 'abbr.csv'), sep='|', header=0)
        abbr_table = abbr_table[~abbr_table.Abbreviation.str.contains('^[A-Z]$')]

        cs_abbr_list = abbr_table[['Abbreviation', 'FullText']][abbr_table.Comment == 'CS']
        lc_abbr_list = abbr_table[['Abbreviation', 'FullText']][abbr_table.Comment.isna()]
        lc_abbr_list.loc[:, 'Abbreviation'] = lc_abbr_list.loc[:, 'Abbreviation'].str.lower()

        for idx in cs_abbr_list.index:
            old_abbr = ' ' + str(cs_abbr_list.loc[idx, 'Abbreviation']) + ' '
            new_full = ' ' + str(cs_abbr_list.loc[idx, 'FullText']) + ' '
            s = s.replace(old_abbr, new_full)

        for idx in lc_abbr_list.index:
            old_abbr = ' ' + str(lc_abbr_list.loc[idx, 'Abbreviation']) + ' '
            new_full = ' ' + str(lc_abbr_list.loc[idx, 'FullText']) + ' '
            s = s.lower().replace(old_abbr, new_full)

        ################################# 3. Puncs #################################
        logger.debug(f'start 3: {datetime.now().strftime("%H:%M:%S")}')

        puncs = re.compile(r'[!"#$%&\'()*+\-,/:;<=>?@[\\\]^_`{|}~\n]+')
        concat_distinct_t_without_puncs = re.sub(puncs, ' ', s)
        concat_distinct_t_without_puncs = re.sub(r'\s+', ' ', concat_distinct_t_without_puncs)

        ###################### 4. Tokenization based on Regex #######################
        logger.debug(f'start 4: {datetime.now().strftime("%H:%M:%S")}')

        p = re.compile(
            r'(?<=\b)[a-z]\.[a-z](?:\.(?:[a-z]|\d))*\.?(?=\s|$)|(?<=\b)[a-z]{1,2}\d{1,2}[a-z]?(?=\s|$)|[a-z]+'
        )
        tokens = p.findall(concat_distinct_t_without_puncs)

        ################################ 5. Stopwords #################################
        logger.debug(f'start 5: {datetime.now().strftime("%H:%M:%S")}')

        tokens = [word for word in tokens if word not in resource_manager.stopwords]

        ############################# 6. Other keywords ##############################
        logger.debug(f'start 6: {datetime.now().strftime("%H:%M:%S")}')

        # Remove other keywords
        to_remove = []
        to_remove.extend(list('abcdefghijklmnopqrstuvwxyz'))
        to_remove.extend([
            'kg',
            'mg',
            'gm',
            'mgmg',
            'ml',
            'litre',
            'cm',
            'km',
            'gram',
            'meter',
            'pm',
            'am',
            'mm',
            'mcg',
            'mgc',
        ])
        to_remove.extend([
            'jan',
            'feb',
            'mar',
            'apr',
            'may',
            'jun',
            'jul',
            'aug',
            'sep',
            'oct',
            'nov',
            'dec',
        ])
        to_remove.extend(['one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten'])
        to_remove.extend([
            'monday',
            'tuesday',
            'wednesday',
            'thursday',
            'friday',
            'mon',
            'tue',
            'wed',
            'thu',
            'fri',
        ])
        to_remove.extend(['per', 'yr', 'another'])

        tokens = [word for word in tokens if word not in to_remove]

        ############################# 7. Spell check ##############################
        logger.debug(f'start 7: {datetime.now().strftime("%H:%M:%S")}')

        corrected_tokens = []
        for w in tokens:
            if len(w) >= 10 and resource_manager.spell_checker is not None:
                repl = resource_manager.spell_checker.lookup(
                    w, Verbosity.TOP, max_edit_distance=1, include_unknown=True
                )[0]
                if (repl.distance == 1) and (repl.term in resource_manager.corpus):
                    corrected_tokens.append(repl.term)
                    continue
            corrected_tokens.append(w)

        ############################# 8. Stemming ##############################
        logger.debug(f'start 8: {datetime.now().strftime("%H:%M:%S")}')

        stemmer = PorterStemmer()
        stemmed_tokens = [stemmer.stem(w) for w in corrected_tokens]

        ############################# 9. Tokenization ##############################
        logger.debug(f'start 9: {datetime.now().strftime("%H:%M:%S")}')

        sent = ' '.join(stemmed_tokens).split('linebreak')

        if resource_manager.tokenizer is not None:
            x = resource_manager.tokenizer.texts_to_matrix(sent, mode='binary')
        else:
            raise RuntimeError('Tokenizer not initialized')

        logger.debug(f'Done: {datetime.now().strftime("%H:%M:%S")}')

        return x, pairids

    except Exception as e:
        logger.error(f'Preprocessing failed: {str(e)}')
        raise


def run(data: str) -> Dict[str, Any]:
    """
    Run inference on the provided data with comprehensive error handling.

    Args:
        data: JSON string containing input data

    Returns:
        Dictionary containing predictions and API version

    Raises:
        ValueError: If input data is invalid
        RuntimeError: If model components are not initialized
        Exception: If inference fails
    """
    try:
        logger.info('Starting inference...')

        # Validate inputs
        if not data:
            raise ValueError('Input data is empty')

        # Get resource manager
        resource_manager = _get_resource_manager()
        if not resource_manager.is_initialized:
            raise RuntimeError('Model or label encoder not initialized. Call init() first.')

        # Get configuration
        config = Config()

        # Preprocess data
        feature_matrix, pair_ids = preprocess(data)
        is_empty = feature_matrix.sum(axis=1) == 0

        logger.info(f'Processing {len(pair_ids)} items')

        # Make predictions with error handling
        try:
            predictions = resource_manager.model.predict(feature_matrix, batch_size=config.BATCH_SIZE)
        except Exception as e:
            logger.error(f'Model prediction failed: {str(e)}')
            raise RuntimeError(f'Model prediction failed: {str(e)}')

        # Get top K predictions
        top_k_indices = predictions.argsort(axis=-1)[:, -config.TOP_K_PREDICTIONS :]
        top_k_probabilities = np.take_along_axis(arr=predictions, indices=top_k_indices, axis=-1)

        # Transform predictions back to original labels
        try:
            pred_1 = resource_manager.label_encoder.inverse_transform(top_k_indices[:, -1])
            pred_2 = resource_manager.label_encoder.inverse_transform(top_k_indices[:, -2])
            pred_3 = resource_manager.label_encoder.inverse_transform(top_k_indices[:, -3])
            pred_4 = resource_manager.label_encoder.inverse_transform(top_k_indices[:, -4])
            pred_5 = resource_manager.label_encoder.inverse_transform(top_k_indices[:, -5])
        except Exception as e:
            logger.error(f'Label transformation failed: {str(e)}')
            raise RuntimeError(f'Label transformation failed: {str(e)}')

        # Apply T202 rule for empty treatments
        pred_1[is_empty] = ['999']
        pred_2[is_empty] = ['NULL']
        pred_3[is_empty] = ['NULL']
        pred_4[is_empty] = ['NULL']
        pred_5[is_empty] = ['NULL']

        top_k_probabilities[is_empty, -1] = 1
        top_k_probabilities[is_empty, -2] = 0
        top_k_probabilities[is_empty, -3] = 0
        top_k_probabilities[is_empty, -4] = 0
        top_k_probabilities[is_empty, -5] = 0

        # Format predictions
        prediction_results = []
        for i in range(len(pair_ids)):
            prediction_dict = {
                'Pair_ID': pair_ids[i],
                'Predict1': pred_1[i],
                'Predict1_confidence': f'{top_k_probabilities[i, -1]:.2f}',
                'Predict2': pred_2[i],
                'Predict2_confidence': f'{top_k_probabilities[i, -2]:.2f}',
                'Predict3': pred_3[i],
                'Predict3_confidence': f'{top_k_probabilities[i, -3]:.2f}',
                'Predict4': pred_4[i],
                'Predict4_confidence': f'{top_k_probabilities[i, -4]:.2f}',
                'Predict5': pred_5[i],
                'Predict5_confidence': f'{top_k_probabilities[i, -5]:.2f}',
            }
            prediction_results.append(prediction_dict)

        result_dict = {'Predictions': prediction_results, 'API_Version_No': config.API_VERSION}

        logger.info(f'Inference completed successfully for {len(prediction_results)} items')
        return result_dict

    except Exception as e:
        logger.error(f'Inference failed: {str(e)}')
        raise


if __name__ == '__main__':
    # Local testing mode - use resource manager for initialization
    try:
        logger.info('Running in local testing mode...')

        # Initialize using the new local file approach
        init()

        # Test inference
        test_data = '[{"Pair_ID": 1001, "T": "Small Animal Consultation", "AmountExVat": 80.91}, {"Pair_ID": 1002, "T": "Cytology(SVC)", "AmountExVat": 63.64}, {"Pair_ID": 1003, "T": "Discount", "AmountExVat": -69}, {"Pair_ID": 1004, "T": "Discount For Multiple Blood Tests", "AmountExVat": -40.32}, {"Pair_ID": 1005, "T": "In House - IDEXX Catalyst Chem 10 Clip", "AmountExVat": 128.68}, {"Pair_ID": 1006, "T": "Discount Given", "AmountExVat": -81.44}]'
        result = run(test_data)
        print(json.dumps(result, indent=2))

    except Exception as e:
        logger.error(f'Local testing failed: {str(e)}')
        raise
